from fastapi import <PERSON><PERSON><PERSON>, API<PERSON><PERSON><PERSON>, HTTPException
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
import requests
from pathlib import Path
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime
import asyncio
from emergentintegrations.llm.chat import LlmChat, UserMessage

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# API Keys
ALPHA_VANTAGE_API_KEY = os.environ['ALPHA_VANTAGE_API_KEY']
NEWSAPI_KEY = os.environ['NEWSAPI_KEY']
GEMINI_API_KEY = os.environ['GEMINI_API_KEY']

# Create the main app without a prefix
app = FastAPI()

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")

# Pydantic Models
class SentimentAnalysis(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    overall_sentiment: str  # "bullish", "bearish", "neutral"
    bullish_score: float  # 0-10
    bearish_score: float  # 0-10
    confidence_score: float  # 0-1
    news_count: int
    analysis_summary: str

class NewsArticle(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    content: str
    source: str
    url: str
    published_at: datetime
    sentiment: str
    relevance_score: float

# Helper Functions
async def fetch_alpha_vantage_news():
    """Fetch financial news from Alpha Vantage"""
    try:
        url = f"https://www.alphavantage.co/query?function=NEWS_SENTIMENT&topics=forex,financial_markets&apikey={ALPHA_VANTAGE_API_KEY}"
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        articles = []
        if "feed" in data:
            for item in data["feed"][:10]:  # Limit to 10 articles
                article = NewsArticle(
                    title=item.get("title", ""),
                    content=item.get("summary", ""),
                    source=item.get("source", "Alpha Vantage"),
                    url=item.get("url", ""),
                    published_at=datetime.fromisoformat(item.get("time_published", "").replace("T", " ")) if item.get("time_published") else datetime.utcnow(),
                    sentiment="neutral",
                    relevance_score=float(item.get("relevance_score", 0.5))
                )
                articles.append(article)
        
        return articles
    except Exception as e:
        logging.error(f"Error fetching Alpha Vantage news: {e}")
        return []

async def fetch_newsapi_news():
    """Fetch financial news from NewsAPI"""
    try:
        url = f"https://newsapi.org/v2/everything?q=forex OR currency OR 'foreign exchange' OR 'central bank'&sortBy=publishedAt&pageSize=10&apiKey={NEWSAPI_KEY}"
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        articles = []
        if "articles" in data:
            for item in data["articles"]:
                if item.get("title") and item.get("content"):
                    article = NewsArticle(
                        title=item["title"],
                        content=item["content"] or item["description"] or "",
                        source=item.get("source", {}).get("name", "NewsAPI"),
                        url=item.get("url", ""),
                        published_at=datetime.fromisoformat(item["publishedAt"].replace("Z", "+00:00")) if item.get("publishedAt") else datetime.utcnow(),
                        sentiment="neutral",
                        relevance_score=0.7
                    )
                    articles.append(article)
        
        return articles
    except Exception as e:
        logging.error(f"Error fetching NewsAPI news: {e}")
        return []

async def analyze_sentiment_with_gemini(articles: List[NewsArticle]):
    """Analyze sentiment using Gemini 2.5 Pro"""
    try:
        # Prepare the news content for analysis
        news_content = "\n\n".join([
            f"Title: {article.title}\nContent: {article.content[:500]}..."
            for article in articles[:15]  # Limit to avoid token limits
        ])
        
        system_message = """You are a professional forex market analyst. Analyze the provided financial news and determine the overall market sentiment.

Provide your analysis in this exact JSON format:
{
  "overall_sentiment": "bullish|bearish|neutral",
  "bullish_score": 0-10,
  "bearish_score": 0-10,
  "confidence_score": 0.0-1.0,
  "analysis_summary": "Brief explanation of your analysis",
  "individual_sentiments": ["sentiment1", "sentiment2", ...]
}

Rules:
- bullish_score: How likely the market will move upward (0-10)
- bearish_score: How likely the market will move downward (0-10)  
- confidence_score: How confident you are in this analysis (0.0-1.0)
- overall_sentiment: The dominant sentiment based on the scores
- analysis_summary: 2-3 sentences explaining key factors
- individual_sentiments: sentiment for each article ("bullish", "bearish", "neutral")

Focus on forex-relevant news: central bank decisions, economic indicators, geopolitical events affecting currencies."""

        chat = LlmChat(
            api_key=GEMINI_API_KEY,
            session_id=str(uuid.uuid4()),
            system_message=system_message
        ).with_model("gemini", "gemini-2.5-pro-preview-05-06").with_max_tokens(4096)
        
        user_message = UserMessage(text=f"Analyze this financial news for forex market sentiment:\n\n{news_content}")
        response = await chat.send_message(user_message)
        
        # Parse the JSON response
        import json
        try:
            analysis_data = json.loads(response.strip().replace("```json", "").replace("```", ""))
            
            # Update individual article sentiments
            individual_sentiments = analysis_data.get("individual_sentiments", [])
            for i, article in enumerate(articles):
                if i < len(individual_sentiments):
                    article.sentiment = individual_sentiments[i]
            
            return SentimentAnalysis(
                overall_sentiment=analysis_data["overall_sentiment"],
                bullish_score=float(analysis_data["bullish_score"]),
                bearish_score=float(analysis_data["bearish_score"]),
                confidence_score=float(analysis_data["confidence_score"]),
                news_count=len(articles),
                analysis_summary=analysis_data["analysis_summary"]
            )
        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            return SentimentAnalysis(
                overall_sentiment="neutral",
                bullish_score=5.0,
                bearish_score=5.0,
                confidence_score=0.5,
                news_count=len(articles),
                analysis_summary="Analysis completed but response parsing failed."
            )
            
    except Exception as e:
        logging.error(f"Error analyzing sentiment with Gemini: {e}")
        return SentimentAnalysis(
            overall_sentiment="neutral",
            bullish_score=5.0,
            bearish_score=5.0,
            confidence_score=0.3,
            news_count=len(articles),
            analysis_summary=f"Analysis failed due to technical error: {str(e)[:100]}..."
        )

# API Endpoints
@api_router.get("/")
async def root():
    return {"message": "Forex Sentiment Analysis API", "status": "active"}

@api_router.post("/analyze")
async def analyze_market_sentiment():
    """Fetch news and analyze market sentiment"""
    try:
        # Fetch news from both sources concurrently
        alpha_articles, newsapi_articles = await asyncio.gather(
            fetch_alpha_vantage_news(),
            fetch_newsapi_news()
        )
        
        # Combine articles
        all_articles = alpha_articles + newsapi_articles
        
        if not all_articles:
            raise HTTPException(status_code=404, detail="No news articles found")
        
        # Analyze sentiment
        sentiment_analysis = await analyze_sentiment_with_gemini(all_articles)
        
        # Store in database
        await db.sentiment_analyses.insert_one(sentiment_analysis.dict())
        for article in all_articles:
            await db.news_articles.insert_one(article.dict())
        
        return {
            "sentiment_analysis": sentiment_analysis,
            "articles": all_articles[:10]  # Return first 10 for display
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in analyze_market_sentiment: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@api_router.get("/sentiment-history")
async def get_sentiment_history(limit: int = 50):
    """Get historical sentiment analysis data for charts"""
    try:
        cursor = db.sentiment_analyses.find().sort("timestamp", -1).limit(limit)
        analyses = await cursor.to_list(length=limit)
        
        # Reverse to get chronological order
        analyses.reverse()
        
        return {
            "analyses": [
                {
                    "id": analysis["id"],
                    "timestamp": analysis["timestamp"].isoformat(),
                    "overall_sentiment": analysis["overall_sentiment"],
                    "bullish_score": analysis["bullish_score"],
                    "bearish_score": analysis["bearish_score"],
                    "confidence_score": analysis["confidence_score"],
                    "news_count": analysis["news_count"],
                    "analysis_summary": analysis["analysis_summary"]
                }
                for analysis in analyses
            ]
        }
    except Exception as e:
        logging.error(f"Error fetching sentiment history: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch sentiment history")

@api_router.get("/latest-news")
async def get_latest_news(limit: int = 20):
    """Get latest news articles"""
    try:
        cursor = db.news_articles.find().sort("published_at", -1).limit(limit)
        articles = await cursor.to_list(length=limit)
        
        return {
            "articles": [
                {
                    "id": article["id"],
                    "title": article["title"],
                    "content": article["content"][:300] + "..." if len(article["content"]) > 300 else article["content"],
                    "source": article["source"],
                    "url": article["url"],
                    "published_at": article["published_at"].isoformat(),
                    "sentiment": article["sentiment"],
                    "relevance_score": article["relevance_score"]
                }
                for article in articles
            ]
        }
    except Exception as e:
        logging.error(f"Error fetching latest news: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch latest news")

@api_router.get("/status")
async def get_api_status():
    """Check API status and connectivity"""
    status = {
        "database": "disconnected",
        "alpha_vantage": "unknown",
        "newsapi": "unknown",
        "gemini": "unknown"
    }
    
    try:
        # Test database
        await db.list_collection_names()
        status["database"] = "connected"
    except:
        pass
    
    # Test Alpha Vantage
    try:
        test_url = f"https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol=EURUSD&interval=1min&apikey={ALPHA_VANTAGE_API_KEY}"
        response = requests.get(test_url, timeout=10)
        if response.status_code == 200 and "Time Series" in response.text:
            status["alpha_vantage"] = "connected"
        else:
            status["alpha_vantage"] = "disconnected"
    except:
        status["alpha_vantage"] = "disconnected"
    
    # Test NewsAPI
    try:
        test_url = f"https://newsapi.org/v2/top-headlines?country=us&pageSize=1&apiKey={NEWSAPI_KEY}"
        response = requests.get(test_url, timeout=10)
        if response.status_code == 200:
            status["newsapi"] = "connected"
        else:
            status["newsapi"] = "disconnected"
    except:
        status["newsapi"] = "disconnected"
    
    # Test Gemini
    try:
        chat = LlmChat(
            api_key=GEMINI_API_KEY,
            session_id="test",
            system_message="Test"
        ).with_model("gemini", "gemini-2.5-pro-preview-05-06")
        test_response = await chat.send_message(UserMessage(text="Say 'test' and nothing else."))
        if test_response:
            status["gemini"] = "connected"
        else:
            status["gemini"] = "disconnected"
    except:
        status["gemini"] = "disconnected"
    
    return status

# Include the router in the main app
app.include_router(api_router)

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()
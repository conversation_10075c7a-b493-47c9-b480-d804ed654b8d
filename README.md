# Forex Market Sentiment Analysis App

A real-time forex market sentiment analysis application that aggregates financial news from multiple sources and uses AI to analyze market sentiment. The app provides live sentiment scores, probability gauges, and trend visualization to help traders make informed decisions.

## 🚀 Features

- **Real-time Sentiment Analysis**: Uses Google Gemini 2.5 Pro to analyze financial news and determine market sentiment
- **Multiple News Sources**: Aggregates news from Alpha Vantage and NewsAPI
- **Interactive Dashboard**: Modern React frontend with real-time charts and gauges
- **Sentiment Scoring**: Provides bullish/bearish probability scores (0-10 scale)
- **Historical Trends**: 24-hour sentiment trend visualization
- **Live News Feed**: Displays analyzed news articles with sentiment indicators
- **MongoDB Storage**: Persistent storage for sentiment analysis history and news articles

## 🏗️ Architecture

### Backend (FastAPI)
- **Framework**: FastAPI with async support
- **Database**: MongoDB with Motor (async driver)
- **AI Integration**: Google Gemini 2.5 Pro via emergentintegrations
- **News Sources**:
  - Alpha Vantage Financial News API
  - NewsAPI
- **Features**: CORS enabled, automatic sentiment analysis, RESTful API

### Frontend (React)
- **Framework**: React 19 with modern hooks
- **UI Components**: Radix UI components with Tailwind CSS
- **Charts**: Recharts for data visualization
- **Build Tool**: CRACO (Create React App Configuration Override)
- **Styling**: Tailwind CSS with custom components

## 📋 Prerequisites

Before running the application, ensure you have:

- **Node.js** (v16 or higher)
- **Python** (v3.8 or higher)
- **MongoDB** (local installation or MongoDB Atlas)
- **Yarn** package manager
- **API Keys** for:
  - Alpha Vantage API
  - NewsAPI
  - Google Gemini API

## 🔧 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd market-sentiment
```

### 2. Backend Setup

#### Install Python Dependencies
```bash
cd backend
pip install -r requirements.txt
```

#### Configure Environment Variables
The backend uses a `.env` file for configuration. Update `backend/.env` with your API keys:

```env
MONGO_URL="mongodb://localhost:27017"
DB_NAME="forex_sentiment_db"

# API Keys (replace with your actual keys)
ALPHA_VANTAGE_API_KEY="your_alpha_vantage_key"
NEWSAPI_KEY="your_newsapi_key"
GEMINI_API_KEY="your_gemini_api_key"
```

#### Start MongoDB
Make sure MongoDB is running locally:
```bash
# On macOS with Homebrew
brew services start mongodb-community

# On Ubuntu/Debian
sudo systemctl start mongod

# Or use MongoDB Atlas (cloud) by updating MONGO_URL in .env
```

#### Run the Backend Server
```bash
cd backend
python -m uvicorn server:app --reload --host 0.0.0.0 --port 8000
```

The backend API will be available at `http://localhost:8000`

### 3. Frontend Setup

#### Install Dependencies
```bash
cd frontend
yarn install
```

#### Configure Environment Variables
Update `frontend/.env` to point to your local backend:

```env
REACT_APP_BACKEND_URL=http://localhost:8000
WDS_SOCKET_PORT=443
```

#### Start the Frontend Development Server
```bash
cd frontend
yarn start
```

The frontend will be available at `http://localhost:3000`

## 🔑 API Keys Setup

### Alpha Vantage API
1. Visit [Alpha Vantage](https://www.alphavantage.co/support/#api-key)
2. Sign up for a free account
3. Get your API key and add it to `backend/.env`

### NewsAPI
1. Visit [NewsAPI](https://newsapi.org/register)
2. Sign up for a free account
3. Get your API key and add it to `backend/.env`

### Google Gemini API
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to `backend/.env`

## 📊 API Endpoints

### Core Endpoints
- `GET /api/` - API status check
- `POST /api/analyze` - Trigger new sentiment analysis
- `GET /api/sentiment-history` - Get historical sentiment data
- `GET /api/latest-news` - Get latest analyzed news articles
- `GET /api/status` - Check API connectivity status

### Example API Response
```json
{
  "sentiment_analysis": {
    "overall_sentiment": "bullish",
    "bullish_score": 7.5,
    "bearish_score": 2.5,
    "confidence_score": 0.85,
    "news_count": 15,
    "analysis_summary": "Strong positive sentiment driven by favorable economic indicators..."
  },
  "articles": [...]
}
```

## 🎯 Usage

1. **Start the Application**: Follow the installation steps above
2. **Refresh Data**: Click the "Refresh" button to fetch latest news and analyze sentiment
3. **View Sentiment**: Monitor the overall sentiment gauge and probability scores
4. **Track Trends**: Use the 24-hour trend chart to see sentiment changes over time
5. **Read News**: Click on news articles to see individual sentiment analysis

## 🛠️ Development

### Project Structure
```
market-sentiment/
├── backend/
│   ├── server.py          # Main FastAPI application
│   ├── requirements.txt   # Python dependencies
│   └── .env              # Environment variables
├── frontend/
│   ├── src/
│   │   ├── App.js        # Main React component
│   │   ├── components/   # UI components
│   │   └── hooks/        # Custom React hooks
│   ├── package.json      # Node.js dependencies
│   └── .env             # Frontend environment variables
└── README.md
```

### Key Dependencies

#### Backend
- `fastapi` - Modern web framework
- `motor` - Async MongoDB driver
- `emergentintegrations` - AI integration library
- `requests` - HTTP client for news APIs
- `pydantic` - Data validation

#### Frontend
- `react` - UI framework
- `recharts` - Chart library
- `axios` - HTTP client
- `@radix-ui/*` - UI component library
- `tailwindcss` - CSS framework

## 🔍 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Ensure MongoDB is running locally
   - Check the `MONGO_URL` in `backend/.env`
   - For MongoDB Atlas, ensure IP whitelist is configured

2. **API Key Errors**
   - Verify all API keys are valid and active
   - Check rate limits for free tier APIs
   - Ensure `.env` file is in the correct location

3. **CORS Issues**
   - Verify `REACT_APP_BACKEND_URL` in `frontend/.env`
   - Check that backend is running on the correct port

4. **News Fetching Fails**
   - Check API key validity
   - Verify internet connection
   - Check API rate limits

### Testing the Setup

1. **Backend Health Check**:
   ```bash
   curl http://localhost:8000/api/status
   ```

2. **Frontend Connection**:
   - Open browser to `http://localhost:3000`
   - Check browser console for errors

## 📈 Performance Considerations

- **Rate Limits**: Be aware of API rate limits for news sources
- **Database**: Consider indexing for better query performance
- **Caching**: Implement caching for frequently accessed data
- **Error Handling**: Robust error handling for external API failures

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the API documentation
3. Check logs for error messages
4. Create an issue in the repository

---

**Note**: This application is for educational and research purposes. Always verify sentiment analysis results with additional research before making trading decisions.